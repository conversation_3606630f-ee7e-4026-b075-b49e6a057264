<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慧习作 - 手机验证码登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #FFFFFF;
            width: 390px;
            height: 844px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
        }

        .container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        /* 状态栏 */
        .status-bar {
            width: 390px;
            height: 40px;
            background: transparent;
            border-bottom: 1px solid #F3F4F6;
            position: relative;
        }

        .signal-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 72px;
            height: 40px;
            border: 1px solid #BCC1CA;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .signal-icons {
            position: relative;
            width: 27.34px;
            height: 10.7px;
            margin-left: 30px;
            margin-top: 16.79px;
        }

        .signal-icon-1 {
            position: absolute;
            left: 0;
            top: 0;
            width: 7.86px;
            height: 10.7px;
        }

        .signal-icon-2 {
            position: absolute;
            left: 9.59px;
            top: 1.61px;
            width: 2.25px;
            height: 7.47px;
        }

        .signal-icon-3 {
            position: absolute;
            left: 13.53px;
            top: 0.25px;
            width: 8.1px;
            height: 10.19px;
        }

        .signal-icon-4 {
            position: absolute;
            left: 22.86px;
            top: 0.25px;
            width: 4.48px;
            height: 10.19px;
        }

        .battery-container {
            position: absolute;
            right: 0;
            top: 0;
            width: 96px;
            height: 40px;
            border: 1px solid #BCC1CA;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .battery-icons {
            position: relative;
            width: 65.87px;
            height: 10.56px;
            margin-left: 12px;
            margin-top: 16.67px;
        }

        .battery-outline {
            position: absolute;
            left: 43.05px;
            top: 0;
            width: 20.28px;
            height: 10.06px;
            opacity: 0.35;
        }

        .battery-fill-1 {
            position: absolute;
            left: 64.68px;
            top: 3.76px;
            width: 1.19px;
            height: 3.59px;
            opacity: 0.4;
        }

        .battery-fill-2 {
            position: absolute;
            left: 44.26px;
            top: 1.2px;
            width: 17.87px;
            height: 7.66px;
        }

        .battery-fill-3 {
            position: absolute;
            left: 22.13px;
            top: 0.35px;
            width: 14.47px;
            height: 10.07px;
        }

        .battery-fill-4 {
            position: absolute;
            left: 8.51px;
            top: 2.05px;
            width: 2.55px;
            height: 8.51px;
        }

        .battery-fill-5 {
            position: absolute;
            left: 12.77px;
            top: 0.35px;
            width: 2.55px;
            height: 10.21px;
        }

        .battery-fill-6 {
            position: absolute;
            left: 4.26px;
            top: 5.03px;
            width: 2.55px;
            height: 5.53px;
        }

        .battery-fill-7 {
            position: absolute;
            left: 0;
            top: 7.16px;
            width: 2.55px;
            height: 3.4px;
        }

        /* 返回按钮 */
        .back-button {
            position: absolute;
            left: 33px;
            top: 52px;
            width: 24px;
            height: 24px;
            cursor: pointer;
        }

        .arrow-group {
            position: relative;
            width: 17.2px;
            height: 12.04px;
            margin-left: 3.4px;
            margin-top: 5.98px;
        }

        .arrow-left-1 {
            position: absolute;
            left: 0;
            top: 6.02px;
            width: 17.2px;
            height: 0;
            stroke: #171A1F;
            stroke-width: 2.064px;
        }

        .arrow-left-2 {
            position: absolute;
            left: 0.02px;
            top: 0;
            width: 6.02px;
            height: 12.04px;
            stroke: #171A1F;
            stroke-width: 2.064px;
        }

        /* 标题区域 */
        .title-section {
            position: absolute;
            left: 33px;
            top: 122px;
        }

        .hello-title {
            font-family: 'Archivo', sans-serif;
            font-weight: 700;
            font-size: 32px;
            line-height: 1.5em;
            color: #171A1F;
            margin-bottom: 0;
        }

        .subtitle {
            font-family: 'Archivo', sans-serif;
            font-weight: 400;
            font-size: 20px;
            line-height: 1.5em;
            color: #9095A0;
            margin-top: 48px;
        }

        /* 表单区域 */
        .form-section {
            position: absolute;
            left: 33px;
            top: 224px;
            width: 324px;
        }

        .input-group {
            margin-bottom: 16px;
        }

        .input-label {
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            font-size: 16px;
            line-height: 1.625em;
            color: #424955;
            margin-bottom: 8px;
            display: block;
        }

        .input-field {
            width: 323px;
            height: 43px;
            background: #F3F4F6;
            border: 1px solid transparent;
            border-radius: 6px;
            padding: 9px 16px;
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 16px;
            line-height: 1.625em;
            color: #171A1F;
        }

        .input-field::placeholder {
            color: #BCC1CA;
        }

        .verification-group {
            position: relative;
            margin-top: 16px;
        }

        .verification-input {
            position: relative;
        }

        .verification-line {
            position: absolute;
            right: 82px;
            top: 50%;
            transform: translateY(-50%);
            width: 1px;
            height: 18px;
            background: #BCC1CA;
        }

        .send-code-btn {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.5714285714285714em;
            color: #636AE8;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            width: 70px;
            text-align: center;
        }

        .send-code-btn:hover {
            color: #5a61d9;
        }

        /* 登录按钮 */
        .login-button {
            position: absolute;
            left: 20px;
            top: 746px;
            width: 350px;
            height: 52px;
            background: #636AE8;
            border: 1px solid transparent;
            border-radius: 16px;
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 18px;
            line-height: 1.5555555555555556em;
            color: #FFFFFF;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-button:hover {
            background: #5a61d9;
        }

        /* 图标样式 */
        .icon {
            width: auto;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="signal-container">
                <div class="signal-icons">
                    <img src="images/signal-icon-1.svg" class="signal-icon-1" alt="信号1">
                    <img src="images/signal-icon-2.svg" class="signal-icon-2" alt="信号2">
                    <img src="images/signal-icon-3.svg" class="signal-icon-3" alt="信号3">
                    <img src="images/signal-icon-4.svg" class="signal-icon-4" alt="信号4">
                </div>
            </div>
            <div class="battery-container">
                <div class="battery-icons">
                    <img src="images/battery-outline.svg" class="battery-outline" alt="电池外框">
                    <img src="images/battery-fill-1.svg" class="battery-fill-1" alt="电池1">
                    <img src="images/battery-fill-2.svg" class="battery-fill-2" alt="电池2">
                    <img src="images/battery-fill-3.svg" class="battery-fill-3" alt="电池3">
                    <img src="images/battery-fill-4.svg" class="battery-fill-4" alt="电池4">
                    <img src="images/battery-fill-5.svg" class="battery-fill-5" alt="电池5">
                    <img src="images/battery-fill-6.svg" class="battery-fill-6" alt="电池6">
                    <img src="images/battery-fill-7.svg" class="battery-fill-7" alt="电池7">
                </div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="back-button">
            <div class="arrow-group">
                <img src="images/arrow-left-1.svg" class="arrow-left-1" alt="返回线">
                <img src="images/arrow-left-2.svg" class="arrow-left-2" alt="返回箭头">
            </div>
        </div>

        <!-- 标题区域 -->
        <div class="title-section">
            <h1 class="hello-title">Hello!</h1>
            <p class="subtitle">请使用手机验证码登录</p>
        </div>

        <!-- 表单区域 -->
        <div class="form-section">
            <div class="input-group">
                <label class="input-label">手机号码</label>
                <input type="tel" class="input-field" value="13860888888">
            </div>
            
            <div class="input-group verification-group">
                <label class="input-label">密码</label>
                <div class="verification-input">
                    <input type="text" class="input-field" placeholder="请输入验证码">
                    <div class="verification-line"></div>
                    <button class="send-code-btn">发送验证码</button>
                </div>
            </div>
        </div>

        <!-- 登录按钮 -->
        <button class="login-button">登录</button>
    </div>
</body>
</html>
